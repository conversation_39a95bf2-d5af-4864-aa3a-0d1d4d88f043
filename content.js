// content.js - 内容脚本，在YouTube页面运行
class YouTubeContentScript {
  constructor() {
    this.init()
  }

  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === "getVideoInfo") {
        this.getVideoInfo().then(sendResponse)
        return true // 保持消息通道开放
      } else if (request.action === "getSearchPageVideos") {
        this.getSearchPageVideos().then(sendResponse)
        return true
      } else if (request.action === "getSubtitles") {
        this.getSubtitles(request.videoId).then(sendResponse)
        return true
      } else if (request.action === "getSubtitleContent") {
        this.getSubtitleContent(request).then(sendResponse)
        return true
      }
    })
  }

  async getVideoInfo() {
    try {
      // 从URL获取视频ID
      const videoId = this.extractVideoId()
      if (!videoId) {
        return { success: false, error: "无法获取视频ID" }
      }

      // 获取视频标题
      const title = this.getVideoTitle()

      return {
        success: true,
        data: {
          videoId: videoId,
          title: title,
          url: window.location.href,
        },
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  extractVideoId() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get("v")
  }

  getVideoTitle() {
    // 尝试多种方式获取视频标题
    let title = ""

    // 方法1: 从页面标题获取
    const pageTitle = document.title
    if (pageTitle && pageTitle !== "YouTube") {
      title = pageTitle.replace(" - YouTube", "")
    }

    // 方法2: 从meta标签获取
    if (!title) {
      const metaTitle = document.querySelector('meta[property="og:title"]')
      if (metaTitle) {
        title = metaTitle.content
      }
    }

    // 方法3: 从h1标签获取
    if (!title) {
      const h1Element = document.querySelector(
        "h1.ytd-video-primary-info-renderer"
      )
      if (h1Element) {
        title = h1Element.textContent.trim()
      }
    }

    // 方法4: 从新版YouTube布局获取
    if (!title) {
      const titleElement = document.querySelector(
        "h1.ytd-watch-metadata yt-formatted-string"
      )
      if (titleElement) {
        title = titleElement.textContent.trim()
      }
    }

    return title || "未知标题"
  }

  // 获取视频时长（可选功能）
  getVideoDuration() {
    const durationElement = document.querySelector(".ytp-time-duration")
    return durationElement ? durationElement.textContent : null
  }

  async getSearchPageVideos() {
    try {
      // 检查是否在YouTube搜索页面或首页
      if (!this.isYouTubeSearchOrHomePage()) {
        return { success: false, error: "当前页面不是YouTube搜索页面或首页" }
      }

      // 获取所有视频链接
      const videoLinks = this.extractVideoLinks()

      if (videoLinks.length === 0) {
        return { success: false, error: "未找到视频链接" }
      }

      return {
        success: true,
        videos: videoLinks,
        count: videoLinks.length,
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  isYouTubeSearchOrHomePage() {
    const url = window.location.href
    return (
      url.includes("youtube.com/results") ||
      (url.includes("youtube.com/") && !url.includes("/watch"))
    )
  }

  extractVideoLinks() {
    const videoLinks = []
    const selectors = [
      // 新版YouTube的选择器
      'a[href*="/watch?v="]',
      // 搜索结果页面的视频链接
      'ytd-video-renderer a[href*="/watch?v="]',
      // 首页推荐视频
      'ytd-rich-item-renderer a[href*="/watch?v="]',
      // 缩略图链接
      'a#thumbnail[href*="/watch?v="]',
      // 视频标题链接
      'a#video-title-link[href*="/watch?v="]',
      // Shorts视频
      'a[href*="/shorts/"]',
    ]

    const processedIds = new Set()

    selectors.forEach((selector) => {
      try {
        const elements = document.querySelectorAll(selector)
        elements.forEach((element) => {
          try {
            if (!element) return

            const href = element.getAttribute("href")
            if (href) {
              // 提取视频ID
              const videoId = this.extractVideoIdFromHref(href)
              if (videoId && !processedIds.has(videoId)) {
                processedIds.add(videoId)
                const fullUrl = href.startsWith("http")
                  ? href
                  : `https://www.youtube.com${href}`
                videoLinks.push({
                  videoId: videoId,
                  url: fullUrl,
                  title: this.getVideoTitle(element),
                })
              }
            }
          } catch (elementError) {
            console.warn("处理单个元素时出错:", elementError)
          }
        })
      } catch (selectorError) {
        console.warn(`选择器 ${selector} 执行失败:`, selectorError)
      }
    })

    return videoLinks
  }

  extractVideoIdFromHref(href) {
    // 处理普通视频链接
    let match = href.match(/[?&]v=([^&]+)/)
    if (match) return match[1]

    // 处理Shorts链接
    match = href.match(/\/shorts\/([^?&]+)/)
    if (match) return match[1]

    return null
  }

  getVideoTitle(element) {
    try {
      // 尝试从不同位置获取视频标题
      let title = ""

      // 方法1: 从aria-label获取
      if (element && element.getAttribute) {
        title = element.getAttribute("aria-label")
        if (title && title.trim()) return title.trim()

        // 方法2: 从title属性获取
        title = element.getAttribute("title")
        if (title && title.trim()) return title.trim()
      }

      // 方法3: 从相邻的标题元素获取
      if (element && element.querySelector) {
        const titleElement =
          element.querySelector("#video-title") ||
          element.querySelector(".ytd-video-meta-block") ||
          element.querySelector("h3 a") ||
          element.querySelector("span[title]")

        if (titleElement) {
          title =
            titleElement.textContent?.trim() ||
            titleElement.getAttribute("title")?.trim()
          if (title) return title
        }
      }

      // 方法4: 从父级元素查找
      if (element && element.closest) {
        const parentRenderer =
          element.closest("ytd-video-renderer") ||
          element.closest("ytd-rich-item-renderer") ||
          element.closest("ytd-compact-video-renderer")

        if (parentRenderer) {
          const titleElement =
            parentRenderer.querySelector("#video-title") ||
            parentRenderer.querySelector("h3 a") ||
            parentRenderer.querySelector("span[title]")

          if (titleElement) {
            title =
              titleElement.textContent?.trim() ||
              titleElement.getAttribute("title")?.trim()
            if (title) return title
          }
        }
      }

      return "未知标题"
    } catch (error) {
      console.warn("获取视频标题时出错:", error)
      return "未知标题"
    }
  }

  // 检查视频是否加载完成
  isVideoLoaded() {
    const video = document.querySelector("video")
    return video && video.readyState >= 2
  }

  // ========== 字幕获取相关方法 ==========

  // 获取字幕列表
  async getSubtitles(videoId) {
    try {
      const transcript = await this.getYouTubeTranscripts(videoId, ["zh", "en"])

      // 转换为与background.js兼容的格式
      const subtitles = [
        {
          name: transcript.language,
          languageCode: transcript.languageCode,
          url: `data:application/json;base64,${btoa(
            JSON.stringify(transcript)
          )}`,
          kind: transcript.isGenerated ? "asr" : "captions",
        },
      ]

      return {
        success: true,
        subtitles: subtitles,
      }
    } catch (error) {
      console.error("获取字幕失败:", error)
      return {
        success: false,
        error: error.message,
      }
    }
  }

  // 获取字幕内容
  async getSubtitleContent(request) {
    try {
      const { subtitle, format = "txt" } = request

      // 从data URL中解析字幕数据
      if (subtitle.url.startsWith("data:application/json;base64,")) {
        const base64Data = subtitle.url.split(",")[1]
        const transcript = JSON.parse(atob(base64Data))

        const content = this.formatTranscriptContent(transcript, format)

        return {
          success: true,
          content: content,
        }
      } else {
        throw new Error("不支持的字幕URL格式")
      }
    } catch (error) {
      console.error("获取字幕内容失败:", error)
      return {
        success: false,
        error: error.message,
      }
    }
  }

  // 主要的字幕获取函数
  async getYouTubeTranscripts(videoId, languages = ["en"]) {
    try {
      // 1. 获取页面中的ytInitialPlayerResponse数据
      const playerResponse = this.extractPlayerResponse()

      // 2. 验证视频可播放性
      this.validatePlayability(playerResponse.playabilityStatus, videoId)

      // 3. 提取字幕轨道信息
      const captionsData = this.extractCaptionsData(playerResponse)

      // 4. 构建字幕列表
      const transcriptList = this.buildTranscriptList(captionsData, languages)

      // 5. 获取并解析字幕数据
      const transcript = await this.fetchTranscriptData(transcriptList)

      return transcript
    } catch (error) {
      console.error("获取字幕失败:", error)
      throw error
    }
  }

  // 从页面提取ytInitialPlayerResponse数据
  extractPlayerResponse() {
    const scripts = document.querySelectorAll("script")

    for (let script of scripts) {
      const content = script.textContent
      if (content && content.includes("var ytInitialPlayerResponse")) {
        const match = content.match(
          /var ytInitialPlayerResponse\s*=\s*({.+?});/
        )
        if (match) {
          try {
            return JSON.parse(match[1])
          } catch (e) {
            continue
          }
        }
      }
    }

    throw new Error("无法找到ytInitialPlayerResponse数据")
  }

  // 验证视频播放状态
  validatePlayability(playabilityStatus, videoId) {
    const status = playabilityStatus?.status
    const reason = playabilityStatus?.reason

    if (status !== "OK") {
      if (status === "LOGIN_REQUIRED") {
        if (reason === "Sign in to confirm you're not a bot") {
          throw new Error("检测到机器人行为，请稍后重试")
        }
        if (reason === "Sign in to confirm your age") {
          throw new Error("年龄限制视频，需要登录")
        }
      }
      if (status === "ERROR" && reason === "Video unavailable") {
        throw new Error(`视频不可用: ${videoId}`)
      }
      throw new Error(`视频无法播放 (${videoId}): ${reason || status}`)
    }
  }

  // 提取字幕数据结构
  extractCaptionsData(playerResponse) {
    const captions = playerResponse?.captions?.playerCaptionsTracklistRenderer

    if (!captions || !captions.captionTracks) {
      throw new Error("该视频没有可用的字幕")
    }

    return captions
  }

  // 构建字幕列表
  buildTranscriptList(captionsData, preferredLanguages) {
    const manualTranscripts = {}
    const generatedTranscripts = {}

    // 分类字幕轨道
    captionsData.captionTracks.forEach((track) => {
      const transcript = {
        videoId: this.getCurrentVideoId(),
        url: track.baseUrl,
        language: track.name.simpleText,
        languageCode: track.languageCode,
        isGenerated: track.kind === "asr",
        isTranslatable: track.isTranslatable || false,
      }

      if (track.kind === "asr") {
        generatedTranscripts[track.languageCode] = transcript
      } else {
        manualTranscripts[track.languageCode] = transcript
      }
    })

    // 根据优先级查找字幕
    for (let langCode of preferredLanguages) {
      if (manualTranscripts[langCode]) {
        return manualTranscripts[langCode]
      }
    }

    for (let langCode of preferredLanguages) {
      if (generatedTranscripts[langCode]) {
        return generatedTranscripts[langCode]
      }
    }

    throw new Error(`未找到指定语言的字幕: ${preferredLanguages.join(", ")}`)
  }

  // 获取并解析字幕数据
  async fetchTranscriptData(transcript) {
    try {
      const response = await fetch(transcript.url)
      const xmlText = await response.text()

      // 解析XML字幕数据
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(xmlText, "application/xml")
      const textElements = xmlDoc.querySelectorAll("text")

      const snippets = Array.from(textElements).map((element) => ({
        text: this.decodeHTMLEntities(element.textContent || ""),
        start: parseFloat(element.getAttribute("start") || "0"),
        duration: parseFloat(element.getAttribute("dur") || "0"),
      }))

      return {
        snippets,
        videoId: transcript.videoId,
        language: transcript.language,
        languageCode: transcript.languageCode,
        isGenerated: transcript.isGenerated,
      }
    } catch (error) {
      throw new Error(`获取字幕数据失败: ${error.message}`)
    }
  }

  // 工具函数：获取当前视频ID
  getCurrentVideoId() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get("v")
  }

  // 工具函数：解码HTML实体
  decodeHTMLEntities(text) {
    const textarea = document.createElement("textarea")
    textarea.innerHTML = text
    return textarea.value
  }

  // 格式化字幕内容
  formatTranscriptContent(transcript, format) {
    switch (format) {
      case "txt":
        return transcript.snippets.map((s) => s.text).join("\n")

      case "srt":
        return transcript.snippets
          .map((s, i) => {
            const start = this.formatTime(s.start)
            const end = this.formatTime(s.start + s.duration)
            return `${i + 1}\n${start} --> ${end}\n${s.text}\n`
          })
          .join("\n")

      case "json":
        return JSON.stringify(transcript, null, 2)

      default:
        return transcript.snippets.map((s) => s.text).join("\n")
    }
  }

  // 工具函数：格式化时间为SRT格式
  formatTime(seconds) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")},${ms
      .toString()
      .padStart(3, "0")}`
  }

  // 字幕下载函数
  downloadTranscript(transcript, format = "txt") {
    let content = ""
    let filename = `${transcript.videoId}_${transcript.languageCode}`

    switch (format) {
      case "txt":
        content = transcript.snippets.map((s) => s.text).join("\n")
        filename += ".txt"
        break

      case "srt":
        content = transcript.snippets
          .map((s, i) => {
            const start = this.formatTime(s.start)
            const end = this.formatTime(s.start + s.duration)
            return `${i + 1}\n${start} --> ${end}\n${s.text}\n`
          })
          .join("\n")
        filename += ".srt"
        break

      case "json":
        content = JSON.stringify(transcript, null, 2)
        filename += ".json"
        break
    }

    // 创建下载链接
    const blob = new Blob([content], { type: "text/plain;charset=utf-8" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()

    URL.revokeObjectURL(url)
  }

  // 使用示例函数
  async downloadCurrentVideoTranscript() {
    try {
      const videoId = this.getCurrentVideoId()
      if (!videoId) {
        throw new Error("无法获取视频ID")
      }

      const transcript = await this.getYouTubeTranscripts(videoId, ["zh", "en"])
      this.downloadTranscript(transcript, "srt")

      console.log("字幕下载成功")
    } catch (error) {
      console.error("下载失败:", error.message)
      alert("下载失败: " + error.message)
    }
  }
}

// 等待页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new YouTubeContentScript();
    });
} else {
    new YouTubeContentScript();
}
