// content.js - 内容脚本，在YouTube页面运行
class YouTubeContentScript {
  constructor() {
    this.init()
  }

  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === "getVideoInfo") {
        this.getVideoInfo().then(sendResponse)
        return true // 保持消息通道开放
      } else if (request.action === "getSearchPageVideos") {
        this.getSearchPageVideos().then(sendResponse)
        return true
      }
    })
  }

  async getVideoInfo() {
    try {
      // 从URL获取视频ID
      const videoId = this.extractVideoId()
      if (!videoId) {
        return { success: false, error: "无法获取视频ID" }
      }

      // 获取视频标题
      const title = this.getVideoTitle()

      return {
        success: true,
        data: {
          videoId: videoId,
          title: title,
          url: window.location.href,
        },
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  extractVideoId() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get("v")
  }

  getVideoTitle() {
    // 尝试多种方式获取视频标题
    let title = ""

    // 方法1: 从页面标题获取
    const pageTitle = document.title
    if (pageTitle && pageTitle !== "YouTube") {
      title = pageTitle.replace(" - YouTube", "")
    }

    // 方法2: 从meta标签获取
    if (!title) {
      const metaTitle = document.querySelector('meta[property="og:title"]')
      if (metaTitle) {
        title = metaTitle.content
      }
    }

    // 方法3: 从h1标签获取
    if (!title) {
      const h1Element = document.querySelector(
        "h1.ytd-video-primary-info-renderer"
      )
      if (h1Element) {
        title = h1Element.textContent.trim()
      }
    }

    // 方法4: 从新版YouTube布局获取
    if (!title) {
      const titleElement = document.querySelector(
        "h1.ytd-watch-metadata yt-formatted-string"
      )
      if (titleElement) {
        title = titleElement.textContent.trim()
      }
    }

    return title || "未知标题"
  }

  // 获取视频时长（可选功能）
  getVideoDuration() {
    const durationElement = document.querySelector(".ytp-time-duration")
    return durationElement ? durationElement.textContent : null
  }

  async getSearchPageVideos() {
    try {
      // 检查是否在YouTube搜索页面或首页
      if (!this.isYouTubeSearchOrHomePage()) {
        return { success: false, error: "当前页面不是YouTube搜索页面或首页" }
      }

      // 获取所有视频链接
      const videoLinks = this.extractVideoLinks()

      if (videoLinks.length === 0) {
        return { success: false, error: "未找到视频链接" }
      }

      return {
        success: true,
        videos: videoLinks,
        count: videoLinks.length,
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  isYouTubeSearchOrHomePage() {
    const url = window.location.href
    return (
      url.includes("youtube.com/results") ||
      (url.includes("youtube.com/") && !url.includes("/watch"))
    )
  }

  extractVideoLinks() {
    const videoLinks = []
    const selectors = [
      // 新版YouTube的选择器
      'a[href*="/watch?v="]',
      // 搜索结果页面的视频链接
      'ytd-video-renderer a[href*="/watch?v="]',
      // 首页推荐视频
      'ytd-rich-item-renderer a[href*="/watch?v="]',
      // 缩略图链接
      'a#thumbnail[href*="/watch?v="]',
      // 视频标题链接
      'a#video-title-link[href*="/watch?v="]',
      // Shorts视频
      'a[href*="/shorts/"]',
    ]

    const processedIds = new Set()

    selectors.forEach((selector) => {
      try {
        const elements = document.querySelectorAll(selector)
        elements.forEach((element) => {
          try {
            if (!element) return

            const href = element.getAttribute("href")
            if (href) {
              // 提取视频ID
              const videoId = this.extractVideoIdFromHref(href)
              if (videoId && !processedIds.has(videoId)) {
                processedIds.add(videoId)
                const fullUrl = href.startsWith("http")
                  ? href
                  : `https://www.youtube.com${href}`
                videoLinks.push({
                  videoId: videoId,
                  url: fullUrl,
                  title: this.getVideoTitle(element),
                })
              }
            }
          } catch (elementError) {
            console.warn("处理单个元素时出错:", elementError)
          }
        })
      } catch (selectorError) {
        console.warn(`选择器 ${selector} 执行失败:`, selectorError)
      }
    })

    return videoLinks
  }

  extractVideoIdFromHref(href) {
    // 处理普通视频链接
    let match = href.match(/[?&]v=([^&]+)/)
    if (match) return match[1]

    // 处理Shorts链接
    match = href.match(/\/shorts\/([^?&]+)/)
    if (match) return match[1]

    return null
  }

  getVideoTitle(element) {
    try {
      // 尝试从不同位置获取视频标题
      let title = ""

      // 方法1: 从aria-label获取
      if (element && element.getAttribute) {
        title = element.getAttribute("aria-label")
        if (title && title.trim()) return title.trim()

        // 方法2: 从title属性获取
        title = element.getAttribute("title")
        if (title && title.trim()) return title.trim()
      }

      // 方法3: 从相邻的标题元素获取
      if (element && element.querySelector) {
        const titleElement =
          element.querySelector("#video-title") ||
          element.querySelector(".ytd-video-meta-block") ||
          element.querySelector("h3 a") ||
          element.querySelector("span[title]")

        if (titleElement) {
          title =
            titleElement.textContent?.trim() ||
            titleElement.getAttribute("title")?.trim()
          if (title) return title
        }
      }

      // 方法4: 从父级元素查找
      if (element && element.closest) {
        const parentRenderer =
          element.closest("ytd-video-renderer") ||
          element.closest("ytd-rich-item-renderer") ||
          element.closest("ytd-compact-video-renderer")

        if (parentRenderer) {
          const titleElement =
            parentRenderer.querySelector("#video-title") ||
            parentRenderer.querySelector("h3 a") ||
            parentRenderer.querySelector("span[title]")

          if (titleElement) {
            title =
              titleElement.textContent?.trim() ||
              titleElement.getAttribute("title")?.trim()
            if (title) return title
          }
        }
      }

      return "未知标题"
    } catch (error) {
      console.warn("获取视频标题时出错:", error)
      return "未知标题"
    }
  }

  // 检查视频是否加载完成
  isVideoLoaded() {
    const video = document.querySelector("video")
    return video && video.readyState >= 2
  }
}

// 等待页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new YouTubeContentScript();
    });
} else {
    new YouTubeContentScript();
}
