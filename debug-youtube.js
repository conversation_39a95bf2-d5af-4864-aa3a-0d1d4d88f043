// YouTube页面结构调试脚本
// 在YouTube页面的浏览器控制台中运行此脚本

function debugYouTubeStructure() {
    console.log('🔍 开始分析YouTube页面结构...');
    
    // 检查页面类型
    const url = window.location.href;
    console.log('当前页面URL:', url);
    
    if (url.includes('/results')) {
        console.log('📄 页面类型: 搜索结果页面');
    } else if (url.includes('/watch')) {
        console.log('📄 页面类型: 视频播放页面');
    } else if (url === 'https://www.youtube.com/' || url === 'https://www.youtube.com') {
        console.log('📄 页面类型: 首页');
    } else {
        console.log('📄 页面类型: 其他页面');
    }
    
    // 测试各种选择器
    const selectors = [
        'a[href*="/watch?v="]',
        'ytd-video-renderer a[href*="/watch?v="]',
        'ytd-rich-item-renderer a[href*="/watch?v="]',
        'a#thumbnail[href*="/watch?v="]',
        'a#video-title-link[href*="/watch?v="]',
        'a[href*="/shorts/"]',
        'ytd-video-renderer',
        'ytd-rich-item-renderer',
        'ytd-compact-video-renderer'
    ];
    
    console.log('\n📊 选择器测试结果:');
    selectors.forEach(selector => {
        try {
            const elements = document.querySelectorAll(selector);
            console.log(`${selector}: ${elements.length} 个元素`);
            
            if (elements.length > 0 && elements.length <= 3) {
                // 显示前几个元素的详细信息
                elements.forEach((el, index) => {
                    if (index < 3) {
                        console.log(`  元素 ${index + 1}:`, {
                            tagName: el.tagName,
                            href: el.getAttribute('href'),
                            title: el.getAttribute('title'),
                            ariaLabel: el.getAttribute('aria-label'),
                            textContent: el.textContent?.substring(0, 50) + '...'
                        });
                    }
                });
            }
        } catch (error) {
            console.error(`${selector}: 错误 -`, error.message);
        }
    });
    
    // 查找视频容器
    console.log('\n🎬 视频容器分析:');
    const videoContainers = [
        'ytd-video-renderer',
        'ytd-rich-item-renderer', 
        'ytd-compact-video-renderer',
        'ytd-grid-video-renderer'
    ];
    
    videoContainers.forEach(container => {
        const elements = document.querySelectorAll(container);
        console.log(`${container}: ${elements.length} 个`);
        
        if (elements.length > 0) {
            const firstElement = elements[0];
            const links = firstElement.querySelectorAll('a[href*="/watch"]');
            console.log(`  内部视频链接: ${links.length} 个`);
            
            if (links.length > 0) {
                console.log(`  第一个链接: ${links[0].getAttribute('href')}`);
            }
        }
    });
    
    // 测试实际的视频链接提取
    console.log('\n🔗 实际链接提取测试:');
    const allVideoLinks = document.querySelectorAll('a[href*="/watch?v="]');
    console.log(`找到 ${allVideoLinks.length} 个视频链接`);
    
    const videoIds = new Set();
    const sampleLinks = [];
    
    allVideoLinks.forEach((link, index) => {
        try {
            const href = link.getAttribute('href');
            if (href) {
                const match = href.match(/[?&]v=([^&]+)/);
                if (match) {
                    const videoId = match[1];
                    if (!videoIds.has(videoId)) {
                        videoIds.add(videoId);
                        if (sampleLinks.length < 5) {
                            sampleLinks.push({
                                index: index,
                                videoId: videoId,
                                href: href,
                                fullUrl: href.startsWith('http') ? href : `https://www.youtube.com${href}`
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.warn(`处理链接 ${index} 时出错:`, error);
        }
    });
    
    console.log(`去重后的视频ID数量: ${videoIds.size}`);
    console.log('示例链接:', sampleLinks);
    
    // 检查页面是否完全加载
    console.log('\n⏳ 页面加载状态:');
    console.log('document.readyState:', document.readyState);
    console.log('页面标题:', document.title);
    
    // 检查是否有动态加载的内容
    setTimeout(() => {
        const newCount = document.querySelectorAll('a[href*="/watch?v="]').length;
        console.log(`\n🔄 5秒后重新检查: ${newCount} 个视频链接`);
    }, 5000);
    
    return {
        totalLinks: allVideoLinks.length,
        uniqueVideos: videoIds.size,
        sampleLinks: sampleLinks
    };
}

// 运行调试
const result = debugYouTubeStructure();
console.log('\n✅ 调试完成，结果:', result);
