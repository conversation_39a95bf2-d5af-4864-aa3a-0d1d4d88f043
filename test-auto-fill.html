<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动获取视频链接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #FF0000;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .youtube-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #FF0000;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .youtube-link:hover {
            background-color: #CC0000;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 自动获取视频链接功能测试</h1>

        <div class="test-section">
            <h3>📋 功能说明</h3>
            <p>新增的"自动获取当前页面视频"按钮可以：</p>
            <ul>
                <li>自动提取YouTube搜索页面的所有视频链接</li>
                <li>自动提取YouTube首页推荐视频链接</li>
                <li>将链接填充到批量下载文本框中</li>
                <li>支持多种YouTube页面布局</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>

            <div class="step">
                <strong>步骤1：</strong> 重新加载Chrome插件
                <br>打开 chrome://extensions/ → 点击插件刷新按钮
                <br><em>（因为修改了manifest.json和content.js）</em>
            </div>

            <div class="step">
                <strong>步骤2：</strong> 打开YouTube搜索页面
                <br>访问以下任一页面进行测试：
                <br>
                <a href="https://www.youtube.com/results?search_query=javascript+tutorial" class="youtube-link" target="_blank">
                    搜索: JavaScript Tutorial
                </a>
                <a href="https://www.youtube.com/results?search_query=python+programming" class="youtube-link" target="_blank">
                    搜索: Python Programming
                </a>
                <a href="https://www.youtube.com/" class="youtube-link" target="_blank">
                    YouTube首页
                </a>
            </div>

            <div class="step">
                <strong>步骤3：</strong> 使用自动获取功能
                <br>1. 在YouTube页面点击插件图标
                <br>2. 切换到"批量处理"标签页
                <br>3. 点击"自动获取当前页面视频"按钮
                <br>4. 观察文本框是否自动填充视频链接
            </div>

            <div class="step">
                <strong>步骤4：</strong> 验证获取结果
                <br>1. 检查文本框中的链接格式是否正确
                <br>2. 验证链接数量是否合理
                <br>3. 测试批量下载/复制功能
            </div>
        </div>

        <div class="test-section">
            <h3>✅ 预期结果</h3>

            <div class="step success">
                <strong>搜索页面：</strong>
                <br>• 应该获取到10-20个视频链接
                <br>• 链接格式：https://www.youtube.com/watch?v=视频ID
                <br>• 每行一个链接，逗号分隔
            </div>

            <div class="step success">
                <strong>首页：</strong>
                <br>• 应该获取到推荐视频链接
                <br>• 数量可能较多（20-50个）
                <br>• 去重处理，无重复链接
            </div>

            <div class="step success">
                <strong>按钮状态：</strong>
                <br>• 点击时显示"获取中..."
                <br>• 完成后恢复原始文本
                <br>• 显示成功消息和获取数量
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 技术实现</h3>

            <div class="note">
                <strong>选择器策略：</strong>
                <div class="code">
// 搜索结果页面的视频链接
'a#thumbnail[href*="/watch?v="]'

// 首页推荐视频
'a#video-title-link[href*="/watch?v="]'

// 通用视频链接
'a[href*="/watch?v="]'
                </div>
            </div>

            <div class="note">
                <strong>去重机制：</strong>
                <br>使用Set存储已处理的视频ID，避免重复链接
            </div>

            <div class="note">
                <strong>错误处理：</strong>
                <br>• 检查是否在YouTube页面
                <br>• 处理content script连接失败
                <br>• 显示详细错误信息
            </div>
        </div>

        <div class="test-section">
            <h3>🐛 常见问题和调试</h3>

            <div class="note">
                <strong>如果获取不到链接：</strong>
                <br>1. 确认在YouTube搜索页面或首页
                <br>2. 页面完全加载后再尝试
                <br>3. 检查浏览器控制台错误信息
                <br>4. 尝试刷新页面后重试
            </div>

            <div class="note">
                <strong>如果按钮无响应：</strong>
                <br>1. 确认插件已重新加载
                <br>2. 检查content script是否正确注入
                <br>3. 查看background.js的service worker日志
            </div>

            <div class="note">
                <strong>调试YouTube页面结构：</strong>
                <br>1. 在YouTube页面按F12打开开发者工具
                <br>2. 在Console标签页粘贴并运行debug-youtube.js脚本
                <br>3. 查看页面结构分析结果
                <br>4. 根据结果调整选择器策略
            </div>

            <div class="note error">
                <strong>YouTube更新导致的问题：</strong>
                <br>YouTube经常更新页面结构，可能导致选择器失效
                <br>症状：报错"Cannot read properties of undefined"
                <br>解决：使用调试脚本分析新的页面结构，更新选择器
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试清单</h3>
            <div class="step">
                <input type="checkbox" id="test1">
                <label for="test1">插件重新加载成功</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test2">
                <label for="test2">在搜索页面能获取到视频链接</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test3">
                <label for="test3">在首页能获取到推荐视频链接</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test4">
                <label for="test4">链接格式正确且无重复</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test5">
                <label for="test5">按钮状态和消息显示正常</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test6">
                <label for="test6">获取的链接可以正常批量处理</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test7">
                <label for="test7">错误处理机制正常工作</label>
            </div>
        </div>
    </div>

    <script>
        // 简单的测试进度跟踪
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const completed = document.querySelectorAll('input[type="checkbox"]:checked').length;
                const total = document.querySelectorAll('input[type="checkbox"]').length;
                console.log(`自动获取功能测试进度: ${completed}/${total}`);

                if (completed === total) {
                    alert('🎉 恭喜！自动获取功能测试全部完成！');
                }
            });
        });
    </script>
</body>
</html>
