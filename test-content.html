<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content.js 字幕获取功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Content.js 字幕获取功能测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>这个页面用于测试重构后的 content.js 中的字幕获取功能。</p>
        <p><strong>注意：</strong>这些功能需要在实际的YouTube视频页面中运行才能正常工作。</p>
    </div>

    <div class="test-section">
        <h3>1. 测试核心函数结构</h3>
        <button class="test-button" onclick="testFunctionStructure()">检查函数结构</button>
        <div id="structure-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试工具函数</h3>
        <button class="test-button" onclick="testUtilityFunctions()">测试工具函数</button>
        <div id="utility-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试HTML实体解码</h3>
        <input type="text" id="html-input" placeholder="输入包含HTML实体的文本" value="&amp;#39;Hello&amp;#39; &amp;quot;World&amp;quot;">
        <button class="test-button" onclick="testHtmlDecode()">测试解码</button>
        <div id="html-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试时间格式化</h3>
        <input type="text" id="time-input" placeholder="输入秒数" value="3661.5">
        <button class="test-button" onclick="testTimeFormat()">测试格式化</button>
        <div id="time-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 测试字幕内容格式化</h3>
        <button class="test-button" onclick="testContentFormat()">测试格式化</button>
        <div id="format-result" class="result"></div>
    </div>

    <script src="content.js"></script>
    <script>
        // 创建测试实例
        const testInstance = new YouTubeContentScript();

        function testFunctionStructure() {
            const result = document.getElementById('structure-result');
            const functions = [
                'getYouTubeTranscripts',
                'extractPlayerResponse',
                'validatePlayability',
                'extractCaptionsData',
                'buildTranscriptList',
                'fetchTranscriptData',
                'getCurrentVideoId',
                'decodeHTMLEntities',
                'formatTranscriptContent',
                'formatTime',
                'downloadTranscript'
            ];

            let output = '检查核心函数是否存在：\n\n';
            let allExists = true;

            functions.forEach(funcName => {
                const exists = typeof testInstance[funcName] === 'function';
                output += `${funcName}: ${exists ? '✓ 存在' : '✗ 不存在'}\n`;
                if (!exists) allExists = false;
            });

            output += `\n总体结果: ${allExists ? '✓ 所有函数都存在' : '✗ 部分函数缺失'}`;
            
            result.textContent = output;
            result.className = `result ${allExists ? 'success' : 'error'}`;
        }

        function testUtilityFunctions() {
            const result = document.getElementById('utility-result');
            let output = '测试工具函数：\n\n';

            try {
                // 测试getCurrentVideoId（在非YouTube页面会返回null）
                const videoId = testInstance.getCurrentVideoId();
                output += `getCurrentVideoId(): ${videoId || 'null (正常，因为不在YouTube页面)'}\n`;

                result.textContent = output;
                result.className = 'result success';
            } catch (error) {
                output += `错误: ${error.message}`;
                result.textContent = output;
                result.className = 'result error';
            }
        }

        function testHtmlDecode() {
            const input = document.getElementById('html-input').value;
            const result = document.getElementById('html-result');

            try {
                const decoded = testInstance.decodeHTMLEntities(input);
                result.textContent = `原文: ${input}\n解码后: ${decoded}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        function testTimeFormat() {
            const input = parseFloat(document.getElementById('time-input').value);
            const result = document.getElementById('time-result');

            try {
                const formatted = testInstance.formatTime(input);
                result.textContent = `输入: ${input} 秒\n格式化结果: ${formatted}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
                result.className = 'result error';
            }
        }

        function testContentFormat() {
            const result = document.getElementById('format-result');

            // 创建测试字幕数据
            const testTranscript = {
                snippets: [
                    { text: "Hello, this is the first subtitle.", start: 0, duration: 3 },
                    { text: "This is the second subtitle.", start: 3.5, duration: 2.5 },
                    { text: "And this is the third one.", start: 6, duration: 4 }
                ],
                videoId: "test123",
                language: "English",
                languageCode: "en",
                isGenerated: false
            };

            try {
                const txtFormat = testInstance.formatTranscriptContent(testTranscript, 'txt');
                const srtFormat = testInstance.formatTranscriptContent(testTranscript, 'srt');
                
                let output = '测试字幕格式化：\n\n';
                output += '=== TXT 格式 ===\n';
                output += txtFormat + '\n\n';
                output += '=== SRT 格式 ===\n';
                output += srtFormat.substring(0, 200) + '...\n';

                result.textContent = output;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
                result.className = 'result error';
            }
        }
    </script>
</body>
</html>
